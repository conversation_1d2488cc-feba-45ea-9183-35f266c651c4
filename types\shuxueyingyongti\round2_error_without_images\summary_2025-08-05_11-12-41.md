## 准确率：62.04%  （(245 - 93) / 245）

## 运行时间: 2025-08-05_11-12-41

## 错题

- 第 1 组响应
- 第 2 组响应
- 第 5 组响应
- 第 7 组响应
- 第 8 组响应
- 第 11 组响应
- 第 12 组响应
- 第 16 组响应
- 第 18 组响应
- 第 22 组响应
- 第 23 组响应
- 第 24 组响应
- 第 32 组响应
- 第 34 组响应
- 第 36 组响应
- 第 38 组响应
- 第 42 组响应
- 第 44 组响应
- 第 45 组响应
- 第 47 组响应
- 第 51 组响应
- 第 52 组响应
- 第 55 组响应
- 第 56 组响应
- 第 58 组响应
- 第 59 组响应
- 第 62 组响应
- 第 64 组响应
- 第 68 组响应
- 第 69 组响应
- 第 71 组响应
- 第 74 组响应
- 第 76 组响应
- 第 86 组响应
- 第 88 组响应
- 第 89 组响应
- 第 94 组响应
- 第 98 组响应
- 第 99 组响应
- 第 101 组响应
- 第 106 组响应
- 第 113 组响应
- 第 115 组响应
- 第 118 组响应
- 第 120 组响应
- 第 122 组响应
- 第 123 组响应
- 第 128 组响应
- 第 129 组响应
- 第 132 组响应
- 第 134 组响应
- 第 136 组响应
- 第 137 组响应
- 第 140 组响应
- 第 141 组响应
- 第 142 组响应
- 第 147 组响应
- 第 151 组响应
- 第 152 组响应
- 第 155 组响应
- 第 158 组响应
- 第 159 组响应
- 第 160 组响应
- 第 161 组响应
- 第 165 组响应
- 第 166 组响应
- 第 170 组响应
- 第 177 组响应
- 第 178 组响应
- 第 179 组响应
- 第 184 组响应
- 第 188 组响应
- 第 191 组响应
- 第 194 组响应
- 第 196 组响应
- 第 198 组响应
- 第 201 组响应
- 第 207 组响应
- 第 211 组响应
- 第 214 组响应
- 第 215 组响应
- 第 216 组响应
- 第 218 组响应
- 第 219 组响应
- 第 220 组响应
- 第 221 组响应
- 第 222 组响应
- 第 233 组响应
- 第 236 组响应
- 第 238 组响应
- 第 239 组响应
- 第 241 组响应
- 第 243 组响应

**批改方式：** JSON比对

**比对说明：** 直接比对学生答案和正确答案的JSON字符串

==================================================
处理第 1 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "178元", "题目2": "NAN"}
```

### 正确答案：
```json
{"题目 1": "178元", "题目 2": "够"}
```

### 比对结果：
```json
{"题目 1":true,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 2 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "178元", "题目2": "NAN"}
```

### 正确答案：
```json
{"题目 1": "178元", "题目 2": "够"}
```

### 比对结果：
```json
{"题目 1":true,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 5 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "400(千米)", "题目2": "3456(棵)"}
```

### 正确答案：
```json
{"题目 1": "320千米", "题目 2": "3456棵"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 7 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "NAN"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 8 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "32千米", "题目2": "3456棵青菜"}
```

### 正确答案：
```json
{"题目 1": "320千米", "题目 2": "3456棵"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 11 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "144元"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":true,"题目 3":true}
```
### 响应时间：0.0000秒


==================================================
处理第 12 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "1454美元", "题目2": "1.79米"}
```

### 正确答案：
```json
{"题目 1": "可兑换1454（美元）", "题目 2": "爸爸的身高是1.79米"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 16 组JSON响应

==================================================
### 学生答案：
```json
{"题目3": "NAN"}
```

### 正确答案：
```json
{"题目 1": "租2辆小客车和1辆大客车最省钱"}
```

### 比对结果：
```json
{"题目 1":false}
```
### 响应时间：0.0000秒


==================================================
处理第 18 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "参加科技类有25人参加艺术类有12人"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 22 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "NAN", "题目2": "3456棵青菜"}
```

### 正确答案：
```json
{"题目 1": "320千米", "题目 2": "3456棵"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 23 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "180元"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":true,"题目 3":false}
```
### 响应时间：0.0000秒


==================================================
处理第 24 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "NAN"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":true,"题目 3":false}
```
### 响应时间：0.0000秒


==================================================
处理第 32 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "238元", "题目2": "NAN"}
```

### 正确答案：
```json
{"题目 1": "178元", "题目 2": "够"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 34 组JSON响应

==================================================
### 学生答案：
```json
{"题目3": "租1辆大客车，2辆小客车，最省钱"}
```

### 正确答案：
```json
{"题目 1": "租2辆小客车和1辆大客车最省钱"}
```

### 比对结果：
```json
{"题目 1":false}
```
### 响应时间：0.0000秒


==================================================
处理第 36 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "18元"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":true,"题目 3":false}
```
### 响应时间：0.0000秒


==================================================
处理第 38 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "307元", "题目2": "NAN"}
```

### 正确答案：
```json
{"题目 1": "178元", "题目 2": "够"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 42 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "NAN"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":true,"题目 3":false}
```
### 响应时间：0.0000秒


==================================================
处理第 44 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "207元", "题目2": "NAN"}
```

### 正确答案：
```json
{"题目 1": "178元", "题目 2": "够"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 45 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "科技组有25人，艺术组有12人"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 47 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "艺术组有12人,科技组有25人"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 51 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "144元"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":true,"题目 3":true}
```
### 响应时间：0.0000秒


==================================================
处理第 52 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "25页", "题目3": "NAN"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false,"题目 3":false}
```
### 响应时间：0.0000秒


==================================================
处理第 55 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "NAN"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 56 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "178元", "题目2": "NAN"}
```

### 正确答案：
```json
{"题目 1": "178元", "题目 2": "够"}
```

### 比对结果：
```json
{"题目 1":true,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 58 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "科技有25名，艺术有12名"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 59 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "25人，艺术类有12人"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 62 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16(dm²)", "题目2": "36", "题目3": "144(元)"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false,"题目 3":false}
```
### 响应时间：0.0000秒


==================================================
处理第 64 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "178元", "题目2": "NAN"}
```

### 正确答案：
```json
{"题目 1": "178元", "题目 2": "够"}
```

### 比对结果：
```json
{"题目 1":true,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 68 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "249元", "题目2": "NAN"}
```

### 正确答案：
```json
{"题目 1": "178元", "题目 2": "够"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 69 组JSON响应

==================================================
### 学生答案：
```json
{"题目4": "科技类25人艺术类12人"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 71 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "NAN"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":true,"题目 3":false}
```
### 响应时间：0.0000秒


==================================================
处理第 74 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "NAN", "题目2": "36页", "题目3": "NAN"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":true,"题目 3":false}
```
### 响应时间：0.0000秒


==================================================
处理第 76 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "218元", "题目2": "NAN"}
```

### 正确答案：
```json
{"题目 1": "178元", "题目 2": "够"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 86 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "1454美元", "题目2": "1.79米"}
```

### 正确答案：
```json
{"题目 1": "9.06元", "题目 2": "1300人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 88 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "1454美元", "题目2": "1.79米"}
```

### 正确答案：
```json
{"题目 1": "可兑换1454（美元）", "题目 2": "爸爸的身高是1.79米"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 89 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "科技类有25人"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 94 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "1454美元", "题目2": "1.79m"}
```

### 正确答案：
```json
{"题目 1": "1454美元", "题目 2": "1.79米"}
```

### 比对结果：
```json
{"题目 1":true,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 98 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm", "题目2": "36页/本", "题目3": "NAN"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false,"题目 3":false}
```
### 响应时间：0.0000秒


==================================================
处理第 99 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "320千米", "题目2": "3456棵"}
```

### 正确答案：
```json
{"题目 1": "320千米", "题目 2": "3456棵"}
```

### 比对结果：
```json
{"题目 1":true,"题目 2":true}
```
### 响应时间：0.0000秒


==================================================
处理第 101 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "120元"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":true,"题目 3":false}
```
### 响应时间：0.0000秒


==================================================
处理第 106 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "400页", "题目3": "144元"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false,"题目 3":true}
```
### 响应时间：0.0000秒


==================================================
处理第 113 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "NAN"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":true,"题目 3":false}
```
### 响应时间：0.0000秒


==================================================
处理第 115 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "640（千米）", "题目2": "3456（棵）"}
```

### 正确答案：
```json
{"题目 1": "320千米", "题目 2": "3456棵"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 118 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "租1辆大车2辆小车最省钱"}
```

### 正确答案：
```json
{"题目 1": "租2辆小客车和1辆大客车最省钱"}
```

### 比对结果：
```json
{"题目 1":false}
```
### 响应时间：0.0000秒


==================================================
处理第 120 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "NAN", "题目3": "NAN"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false,"题目 3":false}
```
### 响应时间：0.0000秒


==================================================
处理第 122 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "320千米", "题目2": "3456棵青菜"}
```

### 正确答案：
```json
{"题目 1": "320千米", "题目 2": "3456棵"}
```

### 比对结果：
```json
{"题目 1":true,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 123 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "NAN"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 128 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "NAN", "题目3": "144元"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false,"题目 3":true}
```
### 响应时间：0.0000秒


==================================================
处理第 129 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "180元"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":true,"题目 3":false}
```
### 响应时间：0.0000秒


==================================================
处理第 132 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "租一辆大客车和2辆小客车最省钱"}
```

### 正确答案：
```json
{"题目 1": "租2辆小客车和1辆大客车最省钱"}
```

### 比对结果：
```json
{"题目 1":false}
```
### 响应时间：0.0000秒


==================================================
处理第 134 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "178元", "题目2": "NAN"}
```

### 正确答案：
```json
{"题目 1": "178元", "题目 2": "够"}
```

### 比对结果：
```json
{"题目 1":true,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 136 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "NAN"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":true,"题目 3":false}
```
### 响应时间：0.0000秒


==================================================
处理第 137 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "NAN"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":true,"题目 3":false}
```
### 响应时间：0.0000秒


==================================================
处理第 140 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "租1辆大车和2辆小车最省钱"}
```

### 正确答案：
```json
{"题目 1": "租2辆小客车和1辆大客车最省钱"}
```

### 比对结果：
```json
{"题目 1":false}
```
### 响应时间：0.0000秒


==================================================
处理第 141 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "科技25名，艺术12名。"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 142 组JSON响应

==================================================
### 学生答案：
```json
{"题目4": "科技25人，艺术12人"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 147 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "36", "题目3": "144元"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false,"题目 3":true}
```
### 响应时间：0.0000秒


==================================================
处理第 151 组JSON响应

==================================================
### 学生答案：
```json
{"题目3": "2辆小1辆大省钱"}
```

### 正确答案：
```json
{"题目 1": "租2辆小客车和1辆大客车最省钱"}
```

### 比对结果：
```json
{"题目 1":false}
```
### 响应时间：0.0000秒


==================================================
处理第 152 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "120千米", "题目2": "NAN"}
```

### 正确答案：
```json
{"题目 1": "320千米", "题目 2": "3456棵"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 155 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "NAN"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":true,"题目 3":false}
```
### 响应时间：0.0000秒


==================================================
处理第 158 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "NAN"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 159 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "NAN"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 160 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "科技类25人，艺术类12人"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 161 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "NAN"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 165 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "36", "题目3": "180元"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false,"题目 3":false}
```
### 响应时间：0.0000秒


==================================================
处理第 166 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "250页", "题目3": "144元"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false,"题目 3":true}
```
### 响应时间：0.0000秒


==================================================
处理第 170 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "1454美元", "题目2": "1.79米"}
```

### 正确答案：
```json
{"题目 1": "1454美元", "题目 2": "1.79米"}
```

### 比对结果：
```json
{"题目 1":true,"题目 2":true}
```
### 响应时间：0.0000秒


==================================================
处理第 177 组JSON响应

==================================================
### 学生答案：
```json
{"题目3": "租一辆大的和2辆小的最省钱"}
```

### 正确答案：
```json
{"题目 1": "租2辆小客车和1辆大客车最省钱"}
```

### 比对结果：
```json
{"题目 1":false}
```
### 响应时间：0.0000秒


==================================================
处理第 178 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "249元", "题目2": "NAN"}
```

### 正确答案：
```json
{"题目 1": "178元", "题目 2": "够"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 179 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "科技类25人,艺术类12人"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 184 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "1454元", "题目2": "1.79m"}
```

### 正确答案：
```json
{"题目 1": "1454美元", "题目 2": "1.79米"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 188 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "400页", "题目3": "144元"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false,"题目 3":true}
```
### 响应时间：0.0000秒


==================================================
处理第 191 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "艺术有12名，科技有25名"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 194 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16 dm²", "题目2": "36页", "题目3": "NAN"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":true,"题目 3":false}
```
### 响应时间：0.0000秒


==================================================
处理第 196 组JSON响应

==================================================
### 学生答案：
```json
{"题目4": "艺术类12人，科技类25人"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 198 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "9.06元", "题目2": "1300人"}
```

### 正确答案：
```json
{"题目 1": "9.06元", "题目 2": "1300人"}
```

### 比对结果：
```json
{"题目 1":true,"题目 2":true}
```
### 响应时间：0.0000秒


==================================================
处理第 201 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "180元", "题目2": "NAN"}
```

### 正确答案：
```json
{"题目 1": "178元", "题目 2": "够"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 207 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "9.06元", "题目2": "1300人"}
```

### 正确答案：
```json
{"题目 1": "9.06元", "题目 2": "1300人"}
```

### 比对结果：
```json
{"题目 1":true,"题目 2":true}
```
### 响应时间：0.0000秒


==================================================
处理第 211 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "144元"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":true,"题目 3":true}
```
### 响应时间：0.0000秒


==================================================
处理第 214 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "1454美元", "题目2": "1.79米"}
```

### 正确答案：
```json
{"题目 1": "1454美元", "题目 2": "1.79米"}
```

### 比对结果：
```json
{"题目 1":true,"题目 2":true}
```
### 响应时间：0.0000秒


==================================================
处理第 215 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "320(千米)", "题目2": "3456(棵)"}
```

### 正确答案：
```json
{"题目 1": "320千米", "题目 2": "3456棵"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 216 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "NAN"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":true,"题目 3":false}
```
### 响应时间：0.0000秒


==================================================
处理第 218 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "180元"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":true,"题目 3":false}
```
### 响应时间：0.0000秒


==================================================
处理第 219 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "16dm²", "题目2": "36页", "题目3": "144元"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":true,"题目 3":true}
```
### 响应时间：0.0000秒


==================================================
处理第 220 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "NAN"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 221 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "NAN"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 222 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "320千米", "题目2": "3456棵青菜"}
```

### 正确答案：
```json
{"题目 1": "320千米", "题目 2": "3456棵"}
```

### 比对结果：
```json
{"题目 1":true,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 233 组JSON响应

==================================================
### 学生答案：
```json
{"题目3": "NAN"}
```

### 正确答案：
```json
{"题目 1": "租2辆小客车和1辆大客车最省钱"}
```

### 比对结果：
```json
{"题目 1":false}
```
### 响应时间：0.0000秒


==================================================
处理第 236 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "科技类有25人，艺术类有12人"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 238 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "32(dm²)", "题目2": "25", "题目3": "144(元)"}
```

### 正确答案：
```json
{"题目 1": "16平方分米", "题目 2": "36页", "题目 3": "144元"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false,"题目 3":false}
```
### 响应时间：0.0000秒


==================================================
处理第 239 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "1454美元", "题目2": "1.79m"}
```

### 正确答案：
```json
{"题目 1": "1454美元", "题目 2": "1.79米"}
```

### 比对结果：
```json
{"题目 1":true,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 241 组JSON响应

==================================================
### 学生答案：
```json
{"题目4": "艺术类有12人，科技类有25人"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


==================================================
处理第 243 组JSON响应

==================================================
### 学生答案：
```json
{"题目1": "艺术类有12人，科技类有25人"}
```

### 正确答案：
```json
{"题目 1": " 科技类 25 人", "题目 2": "艺术类 12 人"}
```

### 比对结果：
```json
{"题目 1":false,"题目 2":false}
```
### 响应时间：0.0000秒


